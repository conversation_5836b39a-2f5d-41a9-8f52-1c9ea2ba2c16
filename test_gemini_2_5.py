#!/usr/bin/env python3
"""
测试 Gemini 2.5 模型调用
验证 Roo Code 插件配置是否正确
"""

import requests
import json
import time

def test_gemini_2_5():
    """测试 Gemini 2.5 模型"""
    
    # 配置信息
    BASE_URL = "https://hitsheubgqzb.ap-southeast-1.clawcloudrun.com"
    AUTH_TOKEN = "12345"
    
    # 测试的 Gemini 2.5 模型
    models_to_test = [
        "gemini-2.5-pro",
        "gemini-2.5-flash",
        "gemini-2.5-pro-preview-03-25",
        "gemini-2.5-flash-preview-05-20"
    ]
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {AUTH_TOKEN}"
    }
    
    print("🧪 测试 Gemini 2.5 模型")
    print("=" * 50)
    print(f"服务地址: {BASE_URL}")
    print(f"认证令牌: {AUTH_TOKEN}")
    print()
    
    for model in models_to_test:
        print(f"🤖 测试模型: {model}")
        
        data = {
            "model": model,
            "messages": [
                {"role": "user", "content": "请简单介绍一下你自己，并说明你的版本"}
            ],
            "max_tokens": 200,
            "temperature": 0.7
        }
        
        try:
            # 添加重试机制和SSL配置
            session = requests.Session()
            # 配置SSL和连接参数
            session.verify = False  # 跳过SSL验证
            session.timeout = 60

            # 添加更多的SSL配置
            import urllib3
            urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

            for attempt in range(3):
                try:
                    response = session.post(
                        f"{BASE_URL}/v1/chat/completions",
                        headers=headers,
                        json=data,
                        timeout=(10, 60)  # (连接超时, 读取超时)
                    )
                    break  # 成功则跳出重试循环
                except (requests.exceptions.SSLError, requests.exceptions.ConnectionError) as e:
                    print(f"   连接错误 (尝试 {attempt + 1}/3): {e}")
                    if attempt < 2:  # 不是最后一次尝试
                        time.sleep(5)  # 等待5秒后重试
                        continue
                    else:
                        raise  # 最后一次尝试失败，抛出异常
            
            if response.status_code == 200:
                result = response.json()
                content = result["choices"][0]["message"]["content"]
                print(f"✅ 成功: {content[:100]}...")
                print()
            else:
                print(f"❌ 失败: HTTP {response.status_code}")
                print(f"   错误: {response.text}")
                print()
                
        except Exception as e:
            print(f"❌ 请求失败: {e}")
            print()
    
    # 测试代码生成能力
    print("💻 测试代码生成能力 (gemini-2.5-pro)")
    
    code_data = {
        "model": "gemini-2.5-pro",
        "messages": [
            {"role": "user", "content": "请写一个Python函数，计算斐波那契数列的第n项"}
        ],
        "max_tokens": 500,
        "temperature": 0.3
    }
    
    try:
        # 使用同样的session配置
        for attempt in range(3):
            try:
                response = session.post(
                    f"{BASE_URL}/v1/chat/completions",
                    headers=headers,
                    json=code_data,
                    timeout=(10, 60)  # (连接超时, 读取超时)
                )
                break  # 成功则跳出重试循环
            except (requests.exceptions.SSLError, requests.exceptions.ConnectionError) as e:
                print(f"   连接错误 (尝试 {attempt + 1}/3): {e}")
                if attempt < 2:  # 不是最后一次尝试
                    time.sleep(5)  # 等待5秒后重试
                    continue
                else:
                    raise  # 最后一次尝试失败，抛出异常
        
        if response.status_code == 200:
            result = response.json()
            content = result["choices"][0]["message"]["content"]
            print(f"✅ 代码生成成功:")
            print(content)
        else:
            print(f"❌ 代码生成失败: HTTP {response.status_code}")
            
    except Exception as e:
        print(f"❌ 代码生成请求失败: {e}")

def generate_roo_code_config():
    """生成 Roo Code 配置示例"""
    
    print("\n" + "=" * 50)
    print("🔧 Roo Code 插件配置")
    print("=" * 50)
    
    config = {
        "provider": "openai",
        "apiKey": "12345",
        "baseURL": "https://hitsheubgqzb.ap-southeast-1.clawcloudrun.com/v1",
        "model": "gemini-2.5-pro",
        "temperature": 0.7,
        "maxTokens": 4096,
        "stream": True
    }
    
    print("📋 配置信息:")
    print(json.dumps(config, indent=2, ensure_ascii=False))
    
    print("\n🎯 推荐模型选择:")
    print("- gemini-2.5-pro: 最强性能，适合复杂代码生成")
    print("- gemini-2.5-flash: 快速响应，适合简单任务")
    print("- gemini-2.5-pro-preview-03-25: 预览版本，可能有新功能")
    
    print("\n⚙️ 参数说明:")
    print("- temperature: 0.3-0.7 适合代码生成")
    print("- maxTokens: 建议 2048-4096")
    print("- stream: 启用流式输出，提升体验")

if __name__ == "__main__":
    test_gemini_2_5()
    generate_roo_code_config()
