#!/usr/bin/env python3
"""
简单测试你的gemini-balance服务
"""

import requests
import json

def simple_test():
    """简单快速测试"""
    
    # 你的服务配置
    BASE_URL = "https://vnupxefbkhtx.ap-northeast-1.clawcloudrun.com"
    AUTH_TOKEN = "12345"
    API_KEY = "AIzaSyDXRI2WiXLC9G1l_JkEbGxxoXTRQQhQpeM"
    
    print("🚀 测试你的gemini-balance服务")
    print("=" * 50)
    print(f"服务地址: {BASE_URL}")
    print(f"认证令牌: {AUTH_TOKEN}")
    print(f"API密钥: {API_KEY[:20]}...")
    print()
    
    # 测试健康检查
    print("📡 健康检查...")
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=10, verify=False)
        if response.status_code == 200:
            print("✅ 服务在线")
        else:
            print(f"⚠️ 服务状态: {response.status_code}")
    except Exception as e:
        print(f"❌ 健康检查失败: {e}")
        return
    
    # 测试简单聊天
    print("\n💬 测试聊天功能...")
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {AUTH_TOKEN}"
    }
    
    data = {
        "model": "gemini-1.5-flash",
        "messages": [
            {"role": "user", "content": "你好，请回复'测试成功'"}
        ]
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/v1/chat/completions",
            headers=headers,
            json=data,
            timeout=60,  # 增加超时时间
            verify=False
        )
        
        if response.status_code == 200:
            result = response.json()
            if "choices" in result and result["choices"]:
                content = result["choices"][0]["message"]["content"]
                print(f"✅ 聊天成功: {content}")
            else:
                print("❌ 响应格式异常")
                print(f"   响应: {result}")
        else:
            print(f"❌ 聊天失败: {response.status_code}")
            print(f"   响应: {response.text}")
    except Exception as e:
        print(f"❌ 聊天请求失败: {e}")
    
    print("\n" + "=" * 50)

def roo_code_config():
    """Roo Code配置信息"""
    
    print("🔧 Roo Code插件配置")
    print("=" * 30)
    print()
    print("📋 配置参数:")
    print("API Provider: OpenAI Compatible")
    print("API Key: 12345")
    print("Base URL: https://vnupxefbkhtx.ap-northeast-1.clawcloudrun.com/v1")
    print("Model: gemini-1.5-flash")
    print("Temperature: 0.7")
    print("Max Tokens: 4096")
    print()
    print("💡 推荐模型:")
    print("- gemini-1.5-flash (快速)")
    print("- gemini-1.5-pro (高质量)")
    print("- gemini-2.0-flash-exp (实验版)")

if __name__ == "__main__":
    simple_test()
    roo_code_config()
