#!/usr/bin/env python3
"""
测试新的gemini-balance服务
增加超时时间和重试机制
"""

import requests
import json

def test_new_service():
    """测试新服务地址"""
    
    BASE_URL = "https://vnupxefbkhtx.ap-northeast-1.clawcloudrun.com"
    AUTH_TOKEN = "12345"
    
    print("🧪 测试新的gemini-balance服务")
    print("=" * 50)
    print(f"服务地址: {BASE_URL}")
    print()
    
    # 测试1: 健康检查
    print("📡 测试1: 健康检查")
    try:
        response = requests.get(
            f"{BASE_URL}/health", 
            timeout=10,
            verify=False
        )
        if response.status_code == 200:
            print("✅ 健康检查成功")
            print(f"   响应: {response.text}")
        else:
            print(f"⚠️ 健康检查状态: {response.status_code}")
    except Exception as e:
        print(f"❌ 健康检查失败: {e}")
    
    # 测试2: 获取模型列表（增加超时）
    print("\n📋 测试2: 获取模型列表")
    headers = {"Authorization": f"Bearer {AUTH_TOKEN}"}
    
    try:
        response = requests.get(
            f"{BASE_URL}/v1/models", 
            headers=headers, 
            timeout=60,  # 增加到60秒
            verify=False
        )
        
        if response.status_code == 200:
            models = response.json()
            print("✅ 模型列表获取成功")
            print(f"   模型数量: {len(models.get('data', []))}")
            # 显示前5个模型
            for model in models.get("data", [])[:5]:
                print(f"   - {model.get('id', 'unknown')}")
        else:
            print(f"❌ 获取模型失败: {response.status_code}")
            print(f"   响应: {response.text}")
    except Exception as e:
        print(f"❌ 模型列表请求失败: {e}")
    
    # 测试3: 简单聊天（增加超时）
    print("\n💬 测试3: 简单聊天")
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {AUTH_TOKEN}"
    }
    
    data = {
        "model": "gemini-1.5-flash",
        "messages": [
            {"role": "user", "content": "请简单回复'服务正常'"}
        ]
    }
    
    try:
        print("   发送请求中...")
        response = requests.post(
            f"{BASE_URL}/v1/chat/completions",
            headers=headers,
            json=data,
            timeout=90,  # 增加到90秒
            verify=False
        )
        
        if response.status_code == 200:
            result = response.json()
            content = result["choices"][0]["message"]["content"]
            print(f"✅ 聊天测试成功: {content}")
        else:
            print(f"❌ 聊天测试失败: {response.status_code}")
            print(f"   响应: {response.text}")
    except Exception as e:
        print(f"❌ 聊天请求失败: {e}")
    
    print("\n" + "=" * 50)
    print("🎯 测试完成")

def generate_roo_code_config():
    """生成Roo Code配置"""
    
    print("\n🔧 Roo Code 插件配置")
    print("=" * 30)
    
    config = {
        "provider": "openai",
        "apiKey": "12345",
        "baseURL": "https://vnupxefbkhtx.ap-northeast-1.clawcloudrun.com/v1",
        "model": "gemini-1.5-flash",
        "temperature": 0.7,
        "maxTokens": 4096,
        "timeout": 90  # 增加超时时间
    }
    
    print("📋 推荐配置:")
    for key, value in config.items():
        print(f"   {key}: {value}")
    
    print("\n💡 注意事项:")
    print("- 服务响应较慢，建议设置较长的超时时间")
    print("- 推荐使用 gemini-1.5-flash 模型（速度较快）")
    print("- 如果超时，可以重试或等待服务优化")

if __name__ == "__main__":
    test_new_service()
    generate_roo_code_config()
