#!/usr/bin/env python3
"""
快速测试 gemini-balance 连接
解决SSL问题
"""

import requests
import json

def quick_test():
    """快速测试连接"""
    
    BASE_URL = "https://vnupxefbkhtx.ap-northeast-1.clawcloudrun.com"
    AUTH_TOKEN = "12345"
    
    print("🔍 快速连接测试")
    print("=" * 40)
    
    # 测试1: 健康检查（跳过SSL验证）
    print("📡 测试1: 健康检查")
    try:
        response = requests.get(
            f"{BASE_URL}/health", 
            timeout=10,
            verify=False  # 跳过SSL验证
        )
        if response.status_code == 200:
            print("✅ 健康检查成功")
        else:
            print(f"⚠️ 健康检查状态: {response.status_code}")
    except Exception as e:
        print(f"❌ 健康检查失败: {e}")
    
    # 测试2: 简单API调用
    print("\n💬 测试2: 简单API调用")
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {AUTH_TOKEN}"
    }
    
    data = {
        "model": "gemini-1.5-flash",
        "messages": [
            {"role": "user", "content": "请回复'连接成功'"}
        ]
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/v1/chat/completions",
            headers=headers,
            json=data,
            timeout=30,
            verify=False  # 跳过SSL验证
        )
        
        if response.status_code == 200:
            result = response.json()
            content = result["choices"][0]["message"]["content"]
            print(f"✅ API调用成功: {content}")
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"   响应: {response.text}")
            
    except requests.exceptions.SSLError as e:
        print(f"❌ SSL错误: {e}")
        print("💡 建议: 服务可能有SSL配置问题")
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    print("\n" + "=" * 40)
    print("🎯 测试完成")

def test_with_curl():
    """提供curl测试命令"""
    print("\n🔧 如果Python测试失败，尝试curl命令:")
    print("=" * 50)
    
    curl_commands = [
        "# 健康检查",
        "curl -k https://hitsheubgqzb.ap-southeast-1.clawcloudrun.com/health",
        "",
        "# API调用测试", 
        'curl -k -X POST https://hitsheubgqzb.ap-southeast-1.clawcloudrun.com/v1/chat/completions \\',
        '  -H "Content-Type: application/json" \\',
        '  -H "Authorization: Bearer 12345" \\',
        '  -d \'{"model": "gemini-1.5-flash", "messages": [{"role": "user", "content": "测试"}]}\'',
        "",
        "# 注意: -k 参数跳过SSL验证"
    ]
    
    for cmd in curl_commands:
        print(cmd)

if __name__ == "__main__":
    quick_test()
    test_with_curl()
